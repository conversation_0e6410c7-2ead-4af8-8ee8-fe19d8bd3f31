graph TD
    A[用户查询] --> B[生成研究计划]
    B --> C[搜索任务执行]
    C --> D[反思分析阶段]
    D --> E[评估决策阶段]
    E --> F{继续研究?}
    F -->|是| G[生成新查询]
    F -->|否| H[研究完成]
    G --> C

    subgraph "反思阶段输入"
        D1[research_topic<br/>研究主题]
        D2[research_plan<br/>原始研究计划]
        D3[current_round_findings<br/>当前轮详细搜索结果]
        D4[previous_queries<br/>上一轮查询建议]
        D5[previous_recommendation<br/>上一轮评估的战略建议]
        D6[current_date<br/>当前日期]
    end

    subgraph "反思阶段处理"
        R1[内容质量分析]
        R2[改进程度评估]
        R3[知识缺口识别]
        R4[生成后续查询]
        R1 --> R2 --> R3 --> R4
    end

    subgraph "反思阶段输出"
        O1[content_quality_score<br/>当前轮内容质量 0-1]
        O2[knowledge_completeness_score<br/>当前轮知识完整性 0-1]
        O3[improvement_analysis<br/>相对上轮的改进程度描述]
        O4[key_discoveries<br/>当轮最重要的发现列表]
        O5[knowledge_gaps<br/>新发现的知识缺口列表]
        O6[follow_up_queries<br/>后续查询建议]
    end

    subgraph "评估阶段输入"
        E1[research_topic<br/>研究主题]
        E2[research_plan<br/>原始研究计划]
        E3[current_reflection<br/>当前轮反思结果JSON]
        E4[all_serp_queries<br/>所有轮次查询关键词]
        E5[all_rounds_discoveries<br/>所有轮次关键发现汇总]
        E6[current_round<br/>当前轮次]
        E7[max_rounds<br/>最大轮次限制]
        E8[max_duration<br/>最长时间限制分钟]
        E9[quality_threshold<br/>质量阈值 0-1]
    end

    subgraph "评估阶段处理"
        V1[审查反思结果]
        V2[全局覆盖评估]
        V3[资源约束检查]
        V4[决策输出]
        V1 --> V2 --> V3 --> V4
    end

    subgraph "评估阶段输出"
        T1[should_continue<br/>是否继续研究]
        T2[resource_constraint_reason<br/>资源限制评估]
        T3[strategic_recommendation<br/>全局战略建议]
        T4[coverage_completeness_score<br/>整体覆盖完整性 0-1]
    end

    subgraph "决策树逻辑"
        Q1{达到最大轮数?}
        Q2{超过时间限制?}
        Q3{覆盖完整性 >= 阈值?}
        Q4{边际收益充足?}
        
        Q1 -->|是| S1[停止: 达到最大轮数]
        Q1 -->|否| Q2
        Q2 -->|是| S2[停止: 超过时间限制]
        Q2 -->|否| Q3
        Q3 -->|是| S3[停止: 达到质量阈值]
        Q3 -->|否| Q4
        Q4 -->|否| S4[停止: 效率不足]
        Q4 -->|是| S5[继续: 需要更多研究]
    end

    %% 样式定义
    classDef startNode fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef processNode fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef searchNode fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef reflectionNode fill:#fff3e0,stroke:#ff9800,stroke-width:3px
    classDef evaluationNode fill:#fce4ec,stroke:#c2185b,stroke-width:3px
    classDef decisionNode fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    classDef endNode fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    classDef inputNode fill:#f5f5f5,stroke:#666,stroke-width:1px
    classDef outputNode fill:#e8f5e8,stroke:#388e3c,stroke-width:1px
    classDef stopNode fill:#ffcdd2,stroke:#d32f2f,stroke-width:2px

    class A startNode
    class B processNode
    class C searchNode
    class D reflectionNode
    class E evaluationNode
    class F decisionNode
    class G searchNode
    class H endNode
    class D1,D2,D3,D4,D5,D6,E1,E2,E3,E4,E5,E6,E7,E8,E9 inputNode
    class O1,O2,O3,O4,O5,O6,T1,T2,T3,T4 outputNode
    class S1,S2,S3,S4 stopNode
    class S5 endNode
