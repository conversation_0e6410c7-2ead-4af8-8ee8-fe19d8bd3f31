{"type": "excalidraw", "version": 2, "source": "https://excalidraw.com", "elements": [{"type": "ellipse", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "start-node", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 100, "y": 100, "strokeColor": "#1976d2", "backgroundColor": "#e3f2fd", "width": 120, "height": 60, "seed": 1, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1, "link": null, "locked": false}, {"type": "text", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "start-text", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 130, "y": 120, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 60, "height": 20, "seed": 1, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1, "link": null, "locked": false, "fontSize": 16, "fontFamily": 1, "text": "用户查询", "textAlign": "center", "verticalAlign": "middle", "containerId": null, "originalText": "用户查询", "lineHeight": 1.25, "baseline": 14}, {"type": "rectangle", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "plan-node", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 300, "y": 100, "strokeColor": "#7b1fa2", "backgroundColor": "#f3e5f5", "width": 140, "height": 60, "seed": 1, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [], "updated": 1, "link": null, "locked": false}, {"type": "text", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "plan-text", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 340, "y": 120, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 60, "height": 20, "seed": 1, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1, "link": null, "locked": false, "fontSize": 14, "fontFamily": 1, "text": "生成研究计划", "textAlign": "center", "verticalAlign": "middle", "containerId": null, "originalText": "生成研究计划", "lineHeight": 1.25, "baseline": 11}, {"type": "rectangle", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "search-node", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 500, "y": 100, "strokeColor": "#f57c00", "backgroundColor": "#fff3e0", "width": 140, "height": 60, "seed": 1, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [], "updated": 1, "link": null, "locked": false}, {"type": "text", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "search-text", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 540, "y": 120, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 60, "height": 20, "seed": 1, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1, "link": null, "locked": false, "fontSize": 14, "fontFamily": 1, "text": "搜索任务执行", "textAlign": "center", "verticalAlign": "middle", "containerId": null, "originalText": "搜索任务执行", "lineHeight": 1.25, "baseline": 11}, {"type": "rectangle", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "reflection-node", "fillStyle": "solid", "strokeWidth": 3, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 700, "y": 80, "strokeColor": "#ff9800", "backgroundColor": "#fff3e0", "width": 160, "height": 100, "seed": 1, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [], "updated": 1, "link": null, "locked": false}, {"type": "text", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "reflection-text", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 750, "y": 120, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 60, "height": 40, "seed": 1, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1, "link": null, "locked": false, "fontSize": 14, "fontFamily": 1, "text": "反思分析阶段\n质量评估\n缺口识别", "textAlign": "center", "verticalAlign": "middle", "containerId": null, "originalText": "反思分析阶段\n质量评估\n缺口识别", "lineHeight": 1.25, "baseline": 32}, {"type": "rectangle", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "evaluation-node", "fillStyle": "solid", "strokeWidth": 3, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 920, "y": 80, "strokeColor": "#c2185b", "backgroundColor": "#fce4ec", "width": 160, "height": 100, "seed": 1, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [], "updated": 1, "link": null, "locked": false}, {"type": "text", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "evaluation-text", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 970, "y": 120, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 60, "height": 40, "seed": 1, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1, "link": null, "locked": false, "fontSize": 14, "fontFamily": 1, "text": "评估决策阶段\n覆盖评估\n约束检查", "textAlign": "center", "verticalAlign": "middle", "containerId": null, "originalText": "评估决策阶段\n覆盖评估\n约束检查", "lineHeight": 1.25, "baseline": 32}], "appState": {"gridSize": null, "viewBackgroundColor": "#ffffff"}, "files": {}}